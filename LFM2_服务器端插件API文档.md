# LFM2 服务器端插件API文档

## 项目概述

LFM2服务器端插件系统基于Delphi开发，通过DLL插件的形式扩展游戏服务器功能。插件系统提供了丰富的API接口，支持NPC脚本命令扩展、自定义菜单、玩家数据操作、游戏逻辑控制等功能。

## 目录结构

```
服务器插件/Delphi/
├── Interface/                    # API接口定义
│   ├── PluginInterface.pas      # 主要API接口
│   ├── PluginTypeDef.pas        # 类型定义
│   └── PluginDelphiUtils.pas    # 实用工具函数
├── 自定义菜单/                  # 自定义菜单示例
│   └── MenuDemo.dpr
└── 自定义Npc脚本命令/           # NPC脚本命令示例
    ├── NpcScriptCmdDemo1.dpr
    ├── NpcScriptCmdDemo2.dpr
    └── 测试脚本.txt
```

---

## 第一部分：核心系统接口

### 1.1 内存管理接口 (TMemory)

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TMemory_Alloc` | 分配内存 | `Size: Integer` | `Pointer` |
| `TMemory_Free` | 释放内存 | `P: Pointer` | 无 |
| `TMemory_Realloc` | 重新分配内存大小 | `P: Pointer; Size: Integer` | 无 |

**使用示例：**
```pascal
var
  Buffer: Pointer;
begin
  Buffer := g_AppFunc.Memory.Alloc(1024);  // 分配1KB内存
  // 使用内存...
  g_AppFunc.Memory.Free(Buffer);           // 释放内存
end;
```

### 1.2 列表管理接口 (TList)

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TList_Create` | 创建列表 | 无 | `_TList` |
| `TList_Free` | 释放列表 | `List: _TList` | 无 |
| `TList_Count` | 获取列表数量 | `List: _TList` | `Integer` |
| `TList_Add` | 添加元素 | `List: _TList; Item: Pointer` | 无 |
| `TList_Insert` | 插入元素 | `List: _TList; Index: Integer; Item: Pointer` | 无 |
| `TList_Remove` | 按元素删除 | `List: _TList; Item: Pointer` | 无 |
| `TList_Delete` | 按索引删除 | `List: _TList; Index: Integer` | 无 |
| `TList_GetItem` | 获取元素 | `List: _TList; Index: Integer` | `Pointer` |
| `TList_SetItem` | 设置元素 | `List: _TList; Index: Integer; Item: Pointer` | 无 |

### 1.3 字符串列表接口 (TStringList)

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TStrList_Create` | 创建文本列表 | 无 | `_TStringList` |
| `TStrList_Free` | 释放文本列表 | `Strings: _TStringList` | 无 |
| `TStrList_Count` | 获取文本数量 | `Strings: _TStringList` | `Integer` |
| `TStrList_Add` | 添加一行 | `Strings: _TStringList; S: PAnsiChar` | 无 |
| `TStrList_GetText` | 获取所有文本内容 | `Strings: _TStringList; Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TStrList_GetItem` | 获取一行的内容 | `Strings: _TStringList; Index: Integer; Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |

---

## 第二部分：M2引擎核心接口

### 2.1 引擎信息接口

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TM2Engine_GetVersion` | 获取M2版本号 | `Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TM2Engine_GetVersionInt` | 获取版本号 | 无 | `Integer` |
| `TM2Engine_GetAppDir` | 获取程序目录 | `Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TM2Engine_GetMainFormHandle` | 获取主窗口句柄 | 无 | `THandle` |
| `TM2Engine_SetMainFormCaption` | 设置主窗口标题 | `Caption: PAnsiChar` | 无 |

### 2.2 全局变量接口

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TM2Engine_GetGlobalVarI` | 获取全局I变量 | `Index: Integer` | `Integer` |
| `TM2Engine_SetGlobalVarI` | 设置全局I变量 | `Index: Integer; Value: Integer` | `BOOL` |
| `TM2Engine_GetGlobalVarG` | 获取全局G变量 | `Index: Integer` | `Integer` |
| `TM2Engine_SetGlobalVarG` | 设置全局G变量 | `Index: Integer; Value: Integer` | `BOOL` |
| `TM2Engine_GetGlobalVarA` | 获取全局A变量 | `Index: Integer; Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TM2Engine_SetGlobalVarA` | 设置全局A变量 | `Index: Integer; Value: PAnsiChar` | `BOOL` |

### 2.3 数据编码解码接口

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TM2Engine_EncodeBuffer` | 编码 | `Src: PAnsiChar; SrcLen: DWORD; Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TM2Engine_DecodeBuffer` | 解码 | `Src: PAnsiChar; SrcLen: DWORD; Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TM2Engine_ZLibEncodeBuffer` | 压缩编码 | `Src: PAnsiChar; SrcLen: DWORD; Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TM2Engine_ZLibDecodeBuffer` | 压缩解码 | `Src: PAnsiChar; SrcLen: DWORD; Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TM2Engine_EncryptPassword` | 密码加密 | `InData: PAnsiChar; OutData: PAnsiChar; var OutSize: DWORD` | `BOOL` |
| `TM2Engine_DecryptPassword` | 密码解密 | `InData: PAnsiChar; OutData: PAnsiChar; var OutSize: DWORD` | `BOOL` |

---

## 第三部分：游戏对象接口

### 3.1 基础对象接口 (TBaseObject)

**基本属性接口：**

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TBaseObject_GetChrName` | 获取角色名称 | `BaseObject: _TBaseObject; Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TBaseObject_SetChrName` | 设置角色名称 | `BaseObject: _TBaseObject; NewName: PAnsiChar` | `BOOL` |
| `TBaseObject_GetGender` | 获取性别 | `BaseObject: _TBaseObject` | `Byte` |
| `TBaseObject_SetGender` | 设置性别 | `BaseObject: _TBaseObject; Gender: Byte` | `BOOL` |
| `TBaseObject_GetJob` | 获取职业 | `BaseObject: _TBaseObject` | `Byte` |
| `TBaseObject_SetJob` | 设置职业 | `BaseObject: _TBaseObject; Job: Byte` | `BOOL` |
| `TBaseObject_GetHair` | 获取发型 | `BaseObject: _TBaseObject` | `Byte` |
| `TBaseObject_SetHair` | 设置发型 | `BaseObject: _TBaseObject; Hair: Byte` | 无 |

**位置相关接口：**

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TBaseObject_GetEnvir` | 所在地图 | `BaseObject: _TBaseObject` | `_TEnvirnoment` |
| `TBaseObject_GetMapName` | 所在地图名称 | `BaseObject: _TBaseObject; Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TBaseObject_GetCurrX` | 当前X坐标 | `BaseObject: _TBaseObject` | `Integer` |
| `TBaseObject_GetCurrY` | 当前Y坐标 | `BaseObject: _TBaseObject` | `Integer` |
| `TBaseObject_GetDirection` | 当前方向 | `BaseObject: _TBaseObject` | `Byte` |
| `TBaseObject_GetHomeMap` | 回城地图 | `BaseObject: _TBaseObject; Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TBaseObject_GetHomeX` | 回城坐标X | `BaseObject: _TBaseObject` | `Integer` |
| `TBaseObject_GetHomeY` | 回城坐标Y | `BaseObject: _TBaseObject` | `Integer` |

**状态相关接口：**

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TBaseObject_GetDeath` | 是否死亡 | `BaseObject: _TBaseObject` | `BOOL` |
| `TBaseObject_GetGhost` | 是否变为幽灵 | `BaseObject: _TBaseObject` | `BOOL` |
| `TBaseObject_MakeGhost` | 杀死变幽灵 | `BaseObject: _TBaseObject` | 无 |
| `TBaseObject_ReAlive` | 复活 | `BaseObject: _TBaseObject` | 无 |
| `TBaseObject_GetCharStatus` | 获取状态 | `BaseObject: _TBaseObject` | `Integer` |
| `TBaseObject_SetCharStatus` | 设置状态 | `BaseObject: _TBaseObject; Value: Integer` | 无 |
