# LFM2 Delphi插件API完整文档

## 项目概述

LFM2是一个基于传奇2引擎的游戏服务器系统，支持通过Delphi开发的DLL插件来扩展功能。该插件系统分为服务器端插件和客户端插件两部分，提供了丰富的API接口用于游戏功能的定制和扩展。

## 目录结构

```
API插件/
├── 服务器插件/
│   └── Delphi/
│       ├── Interface/           # 服务器端API接口定义
│       │   ├── PluginInterface.pas    # 主要API接口
│       │   ├── PluginTypeDef.pas      # 类型定义
│       │   └── PluginDelphiUtils.pas  # 实用工具函数
│       ├── 自定义菜单/          # 自定义菜单示例
│       └── 自定义Npc脚本命令/   # NPC脚本命令示例
└── 客户端插件/
    └── Delphi/
        ├── Common/              # 客户端API接口定义
        │   ├── ClientAPI.pas    # 客户端API接口
        │   └── ClientType.pas   # 客户端类型定义
        └── Demo/                # 客户端插件示例
```

---

## 第一部分：服务器端API接口

### 1. 核心系统接口

#### 1.1 内存管理接口 (TMemory)

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TMemory_Alloc` | 分配内存 | `Size: Integer` | `Pointer` |
| `TMemory_Free` | 释放内存 | `P: Pointer` | 无 |
| `TMemory_Realloc` | 重新分配内存大小 | `P: Pointer; Size: Integer` | 无 |

**使用示例：**
```pascal
var
  Buffer: Pointer;
begin
  Buffer := g_AppFunc.Memory.Alloc(1024);  // 分配1KB内存
  // 使用内存...
  g_AppFunc.Memory.Free(Buffer);           // 释放内存
end;
```

#### 1.2 列表管理接口 (TList)

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TList_Create` | 创建列表 | 无 | `_TList` |
| `TList_Free` | 释放列表 | `List: _TList` | 无 |
| `TList_Count` | 获取列表数量 | `List: _TList` | `Integer` |
| `TList_Add` | 添加元素 | `List: _TList; Item: Pointer` | 无 |
| `TList_Insert` | 插入元素 | `List: _TList; Index: Integer; Item: Pointer` | 无 |
| `TList_Remove` | 按元素删除 | `List: _TList; Item: Pointer` | 无 |
| `TList_Delete` | 按索引删除 | `List: _TList; Index: Integer` | 无 |
| `TList_GetItem` | 获取元素 | `List: _TList; Index: Integer` | `Pointer` |
| `TList_SetItem` | 设置元素 | `List: _TList; Index: Integer; Item: Pointer` | 无 |

#### 1.3 字符串列表接口 (TStringList)

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TStrList_Create` | 创建文本列表 | 无 | `_TStringList` |
| `TStrList_Free` | 释放文本列表 | `Strings: _TStringList` | 无 |
| `TStrList_Count` | 获取文本数量 | `Strings: _TStringList` | `Integer` |
| `TStrList_Add` | 添加一行 | `Strings: _TStringList; S: PAnsiChar` | 无 |
| `TStrList_GetText` | 获取所有文本内容 | `Strings: _TStringList; Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TStrList_GetItem` | 获取一行的内容 | `Strings: _TStringList; Index: Integer; Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |

### 2. M2引擎核心接口

#### 2.1 引擎信息接口

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TM2Engine_GetVersion` | 获取M2版本号 | `Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TM2Engine_GetVersionInt` | 获取版本号 | 无 | `Integer` |
| `TM2Engine_GetAppDir` | 获取程序目录 | `Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TM2Engine_GetMainFormHandle` | 获取主窗口句柄 | 无 | `THandle` |
| `TM2Engine_SetMainFormCaption` | 设置主窗口标题 | `Caption: PAnsiChar` | 无 |

#### 2.2 全局变量接口

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TM2Engine_GetGlobalVarI` | 获取全局I变量 | `Index: Integer` | `Integer` |
| `TM2Engine_SetGlobalVarI` | 设置全局I变量 | `Index: Integer; Value: Integer` | `BOOL` |
| `TM2Engine_GetGlobalVarG` | 获取全局G变量 | `Index: Integer` | `Integer` |
| `TM2Engine_SetGlobalVarG` | 设置全局G变量 | `Index: Integer; Value: Integer` | `BOOL` |
| `TM2Engine_GetGlobalVarA` | 获取全局A变量 | `Index: Integer; Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TM2Engine_SetGlobalVarA` | 设置全局A变量 | `Index: Integer; Value: PAnsiChar` | `BOOL` |

#### 2.3 数据编码解码接口

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TM2Engine_EncodeBuffer` | 编码 | `Src: PAnsiChar; SrcLen: DWORD; Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TM2Engine_DecodeBuffer` | 解码 | `Src: PAnsiChar; SrcLen: DWORD; Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TM2Engine_ZLibEncodeBuffer` | 压缩编码 | `Src: PAnsiChar; SrcLen: DWORD; Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TM2Engine_ZLibDecodeBuffer` | 压缩解码 | `Src: PAnsiChar; SrcLen: DWORD; Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TM2Engine_EncryptPassword` | 密码加密 | `InData: PAnsiChar; OutData: PAnsiChar; var OutSize: DWORD` | `BOOL` |
| `TM2Engine_DecryptPassword` | 密码解密 | `InData: PAnsiChar; OutData: PAnsiChar; var OutSize: DWORD` | `BOOL` |

### 3. 游戏对象接口

#### 3.1 基础对象接口 (TBaseObject)

**基本属性接口：**

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TBaseObject_GetChrName` | 获取角色名称 | `BaseObject: _TBaseObject; Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TBaseObject_SetChrName` | 设置角色名称 | `BaseObject: _TBaseObject; NewName: PAnsiChar` | `BOOL` |
| `TBaseObject_GetGender` | 获取性别 | `BaseObject: _TBaseObject` | `Byte` |
| `TBaseObject_SetGender` | 设置性别 | `BaseObject: _TBaseObject; Gender: Byte` | `BOOL` |
| `TBaseObject_GetJob` | 获取职业 | `BaseObject: _TBaseObject` | `Byte` |
| `TBaseObject_SetJob` | 设置职业 | `BaseObject: _TBaseObject; Job: Byte` | `BOOL` |
| `TBaseObject_GetHair` | 获取发型 | `BaseObject: _TBaseObject` | `Byte` |
| `TBaseObject_SetHair` | 设置发型 | `BaseObject: _TBaseObject; Hair: Byte` | 无 |

**位置相关接口：**

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TBaseObject_GetEnvir` | 所在地图 | `BaseObject: _TBaseObject` | `_TEnvirnoment` |
| `TBaseObject_GetMapName` | 所在地图名称 | `BaseObject: _TBaseObject; Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TBaseObject_GetCurrX` | 当前X坐标 | `BaseObject: _TBaseObject` | `Integer` |
| `TBaseObject_GetCurrY` | 当前Y坐标 | `BaseObject: _TBaseObject` | `Integer` |
| `TBaseObject_GetDirection` | 当前方向 | `BaseObject: _TBaseObject` | `Byte` |
| `TBaseObject_GetHomeMap` | 回城地图 | `BaseObject: _TBaseObject; Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TBaseObject_GetHomeX` | 回城坐标X | `BaseObject: _TBaseObject` | `Integer` |
| `TBaseObject_GetHomeY` | 回城坐标Y | `BaseObject: _TBaseObject` | `Integer` |

**状态相关接口：**

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TBaseObject_GetDeath` | 是否死亡 | `BaseObject: _TBaseObject` | `BOOL` |
| `TBaseObject_GetGhost` | 是否变为幽灵 | `BaseObject: _TBaseObject` | `BOOL` |
| `TBaseObject_MakeGhost` | 杀死变幽灵 | `BaseObject: _TBaseObject` | 无 |
| `TBaseObject_ReAlive` | 复活 | `BaseObject: _TBaseObject` | 无 |
| `TBaseObject_GetCharStatus` | 获取状态 | `BaseObject: _TBaseObject` | `Integer` |
| `TBaseObject_SetCharStatus` | 设置状态 | `BaseObject: _TBaseObject; Value: Integer` | 无 |

**属性相关接口：**

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TBaseObject_GetLevel` | 获取等级 | `BaseObject: _TBaseObject` | `Integer` |
| `TBaseObject_SetLevel` | 设置等级 | `BaseObject: _TBaseObject; Level: Integer` | 无 |
| `TBaseObject_GetExp` | 获取经验值 | `BaseObject: _TBaseObject` | `DWORD` |
| `TBaseObject_SetExp` | 设置经验值 | `BaseObject: _TBaseObject; Exp: DWORD` | 无 |
| `TBaseObject_GetMaxExp` | 获取升级所需经验 | `BaseObject: _TBaseObject` | `DWORD` |
| `TBaseObject_GetHP` | 获取当前HP | `BaseObject: _TBaseObject` | `Integer` |
| `TBaseObject_SetHP` | 设置当前HP | `BaseObject: _TBaseObject; HP: Integer` | 无 |
| `TBaseObject_GetMaxHP` | 获取最大HP | `BaseObject: _TBaseObject` | `Integer` |
| `TBaseObject_SetMaxHP` | 设置最大HP | `BaseObject: _TBaseObject; MaxHP: Integer` | 无 |
| `TBaseObject_GetMP` | 获取当前MP | `BaseObject: _TBaseObject` | `Integer` |
| `TBaseObject_SetMP` | 设置当前MP | `BaseObject: _TBaseObject; MP: Integer` | 无 |
| `TBaseObject_GetMaxMP` | 获取最大MP | `BaseObject: _TBaseObject` | `Integer` |
| `TBaseObject_SetMaxMP` | 设置最大MP | `BaseObject: _TBaseObject; MaxMP: Integer` | 无 |

**攻击防御属性：**

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TBaseObject_GetDC` | 获取攻击力 | `BaseObject: _TBaseObject` | `Integer` |
| `TBaseObject_SetDC` | 设置攻击力 | `BaseObject: _TBaseObject; DC: Integer` | 无 |
| `TBaseObject_GetMaxDC` | 获取最大攻击力 | `BaseObject: _TBaseObject` | `Integer` |
| `TBaseObject_SetMaxDC` | 设置最大攻击力 | `BaseObject: _TBaseObject; MaxDC: Integer` | 无 |
| `TBaseObject_GetMC` | 获取魔法攻击力 | `BaseObject: _TBaseObject` | `Integer` |
| `TBaseObject_SetMC` | 设置魔法攻击力 | `BaseObject: _TBaseObject; MC: Integer` | 无 |
| `TBaseObject_GetMaxMC` | 获取最大魔法攻击力 | `BaseObject: _TBaseObject` | `Integer` |
| `TBaseObject_SetMaxMC` | 设置最大魔法攻击力 | `BaseObject: _TBaseObject; MaxMC: Integer` | 无 |
| `TBaseObject_GetSC` | 获取道术攻击力 | `BaseObject: _TBaseObject` | `Integer` |
| `TBaseObject_SetSC` | 设置道术攻击力 | `BaseObject: _TBaseObject; SC: Integer` | 无 |
| `TBaseObject_GetMaxSC` | 获取最大道术攻击力 | `BaseObject: _TBaseObject` | `Integer` |
| `TBaseObject_SetMaxSC` | 设置最大道术攻击力 | `BaseObject: _TBaseObject; MaxSC: Integer` | 无 |
| `TBaseObject_GetAC` | 获取物理防御 | `BaseObject: _TBaseObject` | `Integer` |
| `TBaseObject_SetAC` | 设置物理防御 | `BaseObject: _TBaseObject; AC: Integer` | 无 |
| `TBaseObject_GetMaxAC` | 获取最大物理防御 | `BaseObject: _TBaseObject` | `Integer` |
| `TBaseObject_SetMaxAC` | 设置最大物理防御 | `BaseObject: _TBaseObject; MaxAC: Integer` | 无 |
| `TBaseObject_GetMAC` | 获取魔法防御 | `BaseObject: _TBaseObject` | `Integer` |
| `TBaseObject_SetMAC` | 设置魔法防御 | `BaseObject: _TBaseObject; MAC: Integer` | 无 |
| `TBaseObject_GetMaxMAC` | 获取最大魔法防御 | `BaseObject: _TBaseObject` | `Integer` |
| `TBaseObject_SetMaxMAC` | 设置最大魔法防御 | `BaseObject: _TBaseObject; MaxMAC: Integer` | 无 |

**特殊状态接口：**

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TBaseObject_GetPoisonValue` | 获取中毒值 | `BaseObject: _TBaseObject` | `DWORD` |
| `TBaseObject_SetPoisonValue` | 设置中毒值 | `BaseObject: _TBaseObject; Value: DWORD` | 无 |
| `TBaseObject_GetIsPoisoned` | 是否中毒 | `BaseObject: _TBaseObject` | `BOOL` |
| `TBaseObject_GetStoneValue` | 获取石化值 | `BaseObject: _TBaseObject` | `DWORD` |
| `TBaseObject_SetStoneValue` | 设置石化值 | `BaseObject: _TBaseObject; Value: DWORD` | 无 |
| `TBaseObject_GetIsStone` | 是否石化 | `BaseObject: _TBaseObject` | `BOOL` |
| `TBaseObject_GetParalysisValue` | 获取麻痹值 | `BaseObject: _TBaseObject` | `DWORD` |
| `TBaseObject_SetParalysisValue` | 设置麻痹值 | `BaseObject: _TBaseObject; Value: DWORD` | 无 |
| `TBaseObject_GetIsParalysis` | 是否麻痹 | `BaseObject: _TBaseObject` | `BOOL` |

**消息发送接口：**

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TBaseObject_SendMsg` | 发送消息 | `BaseObject, Target: _TBaseObject; wIdent, wParam: Integer; nParam1, nParam2, nParam3: NativeInt; sMsg: PAnsiChar` | 无 |
| `TBaseObject_SendDelayMsg` | 发送延时消息 | `BaseObject, Target: _TBaseObject; wIdent, wParam: Integer; nParam1, nParam2, nParam3: NativeInt; sMsg: PAnsiChar; dwDelay: DWORD` | 无 |
| `TBaseObject_SendRefMsg` | 安全发送玩家消息 | `BaseObject: _TBaseObject; wIdent, wParam: Integer; nParam1, nParam2, nParam3: NativeInt; sMsg: PAnsiChar; dwDelay: DWORD` | 无 |
| `TBaseObject_SendUpdateMsg` | 发送更新消息 | `BaseObject, Target: _TBaseObject; wIdent, wParam: Integer; nParam1, nParam2, nParam3: NativeInt; sMsg: PAnsiChar` | 无 |
| `TBaseObject_SysMsg` | 发送系统消息 | `BaseObject: _TBaseObject; sMsg: PAnsiChar; FColor, BColor: Byte; MsgType: Integer` | `BOOL` |

**背包物品接口：**

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TBaseObject_GetBagItemList` | 获取背包物品列表 | `BaseObject: _TBaseObject` | `_TList` |
| `TBaseObject_IsEnoughBag` | 检测背包是否有空位 | `BaseObject: _TBaseObject` | `BOOL` |
| `TBaseObject_IsEnoughBagEx` | 检测背包是否有足够空间放指定数量物品 | `BaseObject: _TBaseObject; AddCount: Integer` | `BOOL` |
| `TBaseObject_AddItemToBag` | 添加物品到背包 | `BaseObject: _TBaseObject; UserItem: pTUserItem` | `BOOL` |
| `TBaseObject_DelBagItemByIndex` | 删除背包中指定索引的物品 | `BaseObject: _TBaseObject; Index: Integer` | `BOOL` |
| `TBaseObject_DelBagItemByMakeIdx` | 根据MakeIndex删除背包物品 | `BaseObject: _TBaseObject; MakeIndex: Integer; ItemName: PAnsiChar` | `BOOL` |
| `TBaseObject_DelBagItemByUserItem` | 根据UserItem删除背包物品 | `BaseObject: _TBaseObject; UserItem: pTUserItem` | `BOOL` |

**移动相关接口：**

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TBaseObject_SpaceMove` | 瞬移到指定地图坐标 | `BaseObject: _TBaseObject; sMapName: PAnsiChar; nX, nY: Integer; nInt: Integer` | 无 |
| `TBaseObject_MapRandomMove` | 瞬移到指定地图随机点 | `BaseObject: _TBaseObject; sMapName: PAnsiChar; nInt: Integer` | 无 |
| `TBaseObject_CanMove` | 角色是否可移动 | `BaseObject: _TBaseObject` | `BOOL` |
| `TBaseObject_CanRun` | 是否可从一个点跑到另一个点 | `BaseObject: _TBaseObject; nCurrX, nCurrY, nX, nY: Integer` | `BOOL` |
| `TBaseObject_TurnTo` | 转向 | `BaseObject: _TBaseObject; btDir: Byte` | 无 |
| `TBaseObject_WalkTo` | 指定方向走一步 | `BaseObject: _TBaseObject; btDir: Byte; boFlag: BOOL` | `BOOL` |
| `TBaseObject_RunTo` | 指定方向跑一步 | `BaseObject: _TBaseObject; btDir: Byte; boFlag: BOOL` | `BOOL` |

#### 3.2 智能对象接口 (TSmartObject)

智能对象继承自基础对象，增加了更多高级功能。

**魔法技能接口：**

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TSmartObject_GetMagicList` | 获取技能列表 | `SmartObject: _TSmartObject` | `_TList` |
| `TSmartObject_IsAllowUseMagic` | 是否允许使用指定技能 | `SmartObject: _TSmartObject; MagicID: Word` | `BOOL` |
| `TSmartObject_SelectMagic` | 选择技能 | `SmartObject: _TSmartObject` | `Integer` |
| `TSmartObject_AttackTarget` | 攻击目标 | `SmartObject: _TSmartObject; MagicID: Word; AttackTime: DWORD` | `BOOL` |

**装备相关接口：**

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TSmartObject_GetUseItem` | 取某个位置的装备 | `SmartObject: _TSmartObject; Index: Integer; UserItem: pTUserItem` | `BOOL` |
| `TSmartObject_RepairAllItem` | 修复所有装备 | `SmartObject: _TSmartObject` | 无 |

**首饰盒接口：**

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TSmartObject_GetJewelryBoxStatus` | 获取首饰盒状态 | `SmartObject: _TSmartObject` | `Integer` |
| `TSmartObject_SetJewelryBoxStatus` | 设置首饰盒状态 | `SmartObject: _TSmartObject; Value: Integer` | 无 |
| `TSmartObject_GetJewelryItem` | 取首饰盒物品 | `SmartObject: _TSmartObject; Index: Integer; UserItem: pTUserItem` | `BOOL` |

**祝福油相关：**

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TSmartObject_GetIsShowGodBless` | 是否显示祝福袋 | `SmartObject: _TSmartObject` | `BOOL` |
| `TSmartObject_SetIsShowGodBless` | 设置显示祝福袋 | `SmartObject: _TSmartObject; Value: BOOL` | 无 |
| `TSmartObject_GetGodBlessItemsState` | 取某个祝福附加的开启状态 | `SmartObject: _TSmartObject; Index: Integer` | `BOOL` |
| `TSmartObject_SetGodBlessItemsState` | 设置祝福附加的开启状态 | `SmartObject: _TSmartObject; Index: Integer; Value: BOOL` | 无 |
| `TSmartObject_GetGodBlessItem` | 取祝福袋物品 | `SmartObject: _TSmartObject; Index: Integer; UserItem: pTUserItem` | `BOOL` |

**封号系统：**

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TSmartObject_GetFengHaoItems` | 封号列表 | `SmartObject: _TSmartObject` | `_TList` |
| `TSmartObject_GetActiveFengHao` | 激活的封号 | `SmartObject: _TSmartObject` | `Integer` |
| `TSmartObject_SetActiveFengHao` | 设置当前激活封号 | `SmartObject: _TSmartObject; FengHaoIndex: Integer` | 无 |
| `TSmartObject_ActiveFengHaoChanged` | 刷新封号到客户端 | `SmartObject: _TSmartObject` | 无 |
| `TSmartObject_DeleteFengHao` | 删除封号 | `SmartObject: _TSmartObject; Index: Integer` | 无 |
| `TSmartObject_ClearFengHao` | 清空封号 | `SmartObject: _TSmartObject` | 无 |

**游戏速度相关：**

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TSmartObject_GetMoveSpeed` | 获取移动速度 | `SmartObject: _TSmartObject` | `SmallInt` |
| `TSmartObject_SetMoveSpeed` | 设置移动速度 | `SmartObject: _TSmartObject; Value: SmallInt` | 无 |
| `TSmartObject_GetAttackSpeed` | 获取攻击速度 | `SmartObject: _TSmartObject` | `SmallInt` |
| `TSmartObject_SetAttackSpeed` | 设置攻击速度 | `SmartObject: _TSmartObject; Value: SmallInt` | 无 |
| `TSmartObject_GetSpellSpeed` | 获取魔法速度 | `SmartObject: _TSmartObject` | `SmallInt` |
| `TSmartObject_SetSpellSpeed` | 设置魔法速度 | `SmartObject: _TSmartObject; Value: SmallInt` | 无 |
| `TSmartObject_RefGameSpeed` | 刷新游戏速度 | `SmartObject: _TSmartObject` | 无 |

**特殊物品效果：**

| 函数名 | 功能描述 | 参数 | 返回值 | 备注 |
|--------|----------|------|--------|------|
| `TSmartObject_GetIsTeleport` | 传送戒指 | `SmartObject: _TSmartObject` | `BOOL` | 物品类型:112 |
| `TSmartObject_SetIsTeleport` | 设置传送戒指 | `SmartObject: _TSmartObject; Value: BOOL` | 无 | 物品类型:112 |
| `TSmartObject_GetIsRevival` | 复活戒指 | `SmartObject: _TSmartObject` | `BOOL` | 物品类型:114 |
| `TSmartObject_SetIsRevival` | 设置复活戒指 | `SmartObject: _TSmartObject; Value: BOOL` | 无 | 物品类型:114 |
| `TSmartObject_GetRevivalTime` | 复活戒指: 复活时间 | `SmartObject: _TSmartObject` | `Integer` | 单位：秒 |
| `TSmartObject_SetRevivalTime` | 设置复活时间 | `SmartObject: _TSmartObject; Value: Integer` | 无 | 单位：秒 |
| `TSmartObject_GetIsFlameRing` | 火焰戒指 | `SmartObject: _TSmartObject` | `BOOL` | 物品类型:115 |
| `TSmartObject_SetIsFlameRing` | 设置火焰戒指 | `SmartObject: _TSmartObject; Value: BOOL` | 无 | 物品类型:115 |
| `TSmartObject_GetIsRecoveryRing` | 治愈戒指 | `SmartObject: _TSmartObject` | `BOOL` | 物品类型:116 |
| `TSmartObject_SetIsRecoveryRing` | 设置治愈戒指 | `SmartObject: _TSmartObject; Value: BOOL` | 无 | 物品类型:116 |
| `TSmartObject_GetIsMagicShield` | 护身戒指 | `SmartObject: _TSmartObject` | `BOOL` | 物品类型:118 |
| `TSmartObject_SetIsMagicShield` | 设置护身戒指 | `SmartObject: _TSmartObject; Value: BOOL` | 无 | 物品类型:118 |
| `TSmartObject_GetIsMuscleRing` | 力量戒指(麻痹) | `SmartObject: _TSmartObject` | `BOOL` | 物品类型:119 |
| `TSmartObject_SetIsMuscleRing` | 设置力量戒指 | `SmartObject: _TSmartObject; Value: BOOL` | 无 | 物品类型:119 |
| `TSmartObject_GetIsFastTrain` | 技巧项链 | `SmartObject: _TSmartObject` | `BOOL` | 物品类型:120 |
| `TSmartObject_SetIsFastTrain` | 设置技巧项链 | `SmartObject: _TSmartObject; Value: BOOL` | 无 | 物品类型:120 |
| `TSmartObject_GetIsProbeNecklace` | 探测项链 | `SmartObject: _TSmartObject` | `BOOL` | 物品类型:121 |
| `TSmartObject_SetIsProbeNecklace` | 设置探测项链 | `SmartObject: _TSmartObject; Value: BOOL` | 无 | 物品类型:121 |

**PK相关接口：**

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TSmartObject_GetPKPoint` | 获取PK值 | `SmartObject: _TSmartObject` | `Integer` |
| `TSmartObject_SetPKPoint` | 设置PK值 | `SmartObject: _TSmartObject; Value: Integer` | 无 |
| `TSmartObject_IncPKPoint` | 增加PK值 | `SmartObject: _TSmartObject; Value: Integer` | 无 |
| `TSmartObject_DecPKPoint` | 减少PK值 | `SmartObject: _TSmartObject; Value: Integer` | 无 |
| `TSmartObject_GetPKLevel` | 获取PK等级 | `SmartObject: _TSmartObject` | `Integer` |
| `TSmartObject_SetPKLevel` | 设置PK等级 | `SmartObject: _TSmartObject; Value: Integer` | 无 |
| `TSmartObject_GetPKDieLostExp` | PK 死亡损失经验，用于计算掉的等级 | `SmartObject: _TSmartObject` | `DWORD` |
| `TSmartObject_SetPKDieLostExp` | 设置PK死亡损失经验 | `SmartObject: _TSmartObject; Value: DWORD` | 无 |
| `TSmartObject_GetPKDieLostLevel` | PK 死亡掉等级 | `SmartObject: _TSmartObject` | `Integer` |
| `TSmartObject_SetPKDieLostLevel` | 设置PK死亡掉等级 | `SmartObject: _TSmartObject; Value: Integer` | 无 |

#### 3.3 玩家对象接口 (TPlayObject)

玩家对象继承自智能对象，是最完整的角色对象类型。

**基本信息接口：**

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TPlayObject_GetUserID` | 获取账号 | `Player: _TPlayObject; Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TPlayObject_GetIPAddr` | 获取IP地址 | `Player: _TPlayObject; Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TPlayObject_GetIPLocal` | 获取IP归属地 | `Player: _TPlayObject; Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TPlayObject_GetMachineID` | 获取MAC地址 | `Player: _TPlayObject; Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TPlayObject_GetIsReadyRun` | 是否进入游戏中 | `Player: _TPlayObject` | `BOOL` |
| `TPlayObject_GetLogonTime` | 获取登录时间 | `Player: _TPlayObject; LogonTime: PSystemTime` | `BOOL` |
| `TPlayObject_GetSoftVerDate` | 获取客户端版本号 | `Player: _TPlayObject` | `Integer` |
| `TPlayObject_GetClientType` | 获取客户端类型 | `Player: _TPlayObject` | `Integer` |
| `TPlayObject_IsOldClient` | 是否为旧客户端 | `Player: _TPlayObject` | `BOOL` |
| `TPlayObject_GetScreenWidth` | 客户端分辨率 宽 | `Player: _TPlayObject` | `Word` |
| `TPlayObject_GetScreenHeight` | 客户端分辨率 高 | `Player: _TPlayObject` | `Word` |
| `TPlayObject_GetClientViewRange` | 客户端视野范围大小 | `Player: _TPlayObject` | `Word` |

**转生和属性点：**

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TPlayObject_GetRelevel` | 获取转生等级 | `Player: _TPlayObject` | `Byte` |
| `TPlayObject_SetRelevel` | 设置转生等级 | `Player: _TPlayObject; Value: Byte` | 无 |
| `TPlayObject_GetBonusPoint` | 获取未分配属性点 | `Player: _TPlayObject` | `Integer` |
| `TPlayObject_SetBonusPoint` | 设置未分配属性点 | `Player: _TPlayObject; Value: Integer` | 无 |
| `TPlayObject_SendAdjustBonus` | 发送分配属性点 | `Player: _TPlayObject` | 无 |

**英雄相关：**

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TPlayObject_GetHeroName` | 获取主英雄名称 | `Player: _TPlayObject; Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TPlayObject_GetDeputyHeroName` | 获取副英雄名称 | `Player: _TPlayObject; Dest: PAnsiChar; var DestLen: DWORD` | `BOOL` |
| `TPlayObject_GetDeputyHeroJob` | 获取副英雄职业 | `Player: _TPlayObject` | `Byte` |
| `TPlayObject_GetMyHero` | 获取英雄对象 | `Player: _TPlayObject` | `_THeroObject` |
| `TPlayObject_GetFixedHero` | 是否锁定召唤英雄 | `Player: _TPlayObject` | `BOOL` |
| `TPlayObject_ClientHeroLogOn` | 召唤英雄 | `Player: _TPlayObject; IsDeputyHero: BOOL` | 无 |
| `TPlayObject_GetStorageHero` | 英雄是否在仓库 | `Player: _TPlayObject` | `BOOL` |
| `TPlayObject_GetStorageDeputyHero` | 副英雄是否在仓库 | `Player: _TPlayObject` | `BOOL` |

**仓库相关：**

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TPlayObject_GetIsStorageOpen` | 仓库是否打开 | `Player: _TPlayObject; Index: Integer` | `BOOL` |
| `TPlayObject_SetIsStorageOpen` | 设置仓库是否打开 | `Player: _TPlayObject; Index: Integer; Value: BOOL` | 无 |

**货币系统：**

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TPlayObject_GetGold` | 获取金币 | `Player: _TPlayObject` | `DWORD` |
| `TPlayObject_SetGold` | 设置金币 | `Player: _TPlayObject; Value: DWORD` | 无 |
| `TPlayObject_GetGoldMax` | 获取金币最大可携带量 | `Player: _TPlayObject` | `DWORD` |
| `TPlayObject_IncGold` | 增加金币 | `Player: _TPlayObject; Value: DWORD` | `BOOL` |
| `TPlayObject_DecGold` | 减少金币 | `Player: _TPlayObject; Value: DWORD` | `BOOL` |
| `TPlayObject_GoldChanged` | 通知客户端刷新 (金币、元宝) | `Player: _TPlayObject` | 无 |
| `TPlayObject_GetGameGold` | 获取元宝数量 | `Player: _TPlayObject` | `DWORD` |
| `TPlayObject_SetGameGold` | 设置元宝数量 | `Player: _TPlayObject; Value: DWORD` | 无 |
| `TPlayObject_IncGameGold` | 增加元宝 | `Player: _TPlayObject; Value: DWORD` | 无 |
| `TPlayObject_DecGameGold` | 减少元宝 | `Player: _TPlayObject; Value: DWORD` | 无 |
| `TPlayObject_GameGoldChanged` | 通知客户端刷新 (元宝、游戏币) | `Player: _TPlayObject` | 无 |
| `TPlayObject_GetGamePoint` | 获取游戏币 | `Player: _TPlayObject` | `DWORD` |
| `TPlayObject_SetGamePoint` | 设置游戏币 | `Player: _TPlayObject; Value: DWORD` | 无 |
| `TPlayObject_IncGamePoint` | 增加游戏币 | `Player: _TPlayObject; Value: DWORD` | 无 |
| `TPlayObject_DecGamePoint` | 减少游戏币 | `Player: _TPlayObject; Value: DWORD` | 无 |
| `TPlayObject_GetGameDiamond` | 获取钻石 | `Player: _TPlayObject` | `DWORD` |
| `TPlayObject_SetGameDiamond` | 设置钻石 | `Player: _TPlayObject; Value: DWORD` | 无 |
| `TPlayObject_IncGameDiamond` | 增加钻石 | `Player: _TPlayObject; Value: DWORD` | 无 |
| `TPlayObject_DecGameDiamond` | 减少钻石 | `Player: _TPlayObject; Value: DWORD` | 无 |
| `TPlayObject_NewGamePointChanged` | 通知客户端刷新 (钻石、积分) | `Player: _TPlayObject` | 无 |
| `TPlayObject_GetGameGird` | 获取积分 | `Player: _TPlayObject` | `DWORD` |
| `TPlayObject_SetGameGird` | 设置积分 | `Player: _TPlayObject; Value: DWORD` | 无 |
| `TPlayObject_IncGameGird` | 增加积分 | `Player: _TPlayObject; Value: DWORD` | 无 |
| `TPlayObject_DecGameGird` | 减少积分 | `Player: _TPlayObject; Value: DWORD` | 无 |

**其他货币：**

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TPlayObject_GetGameGoldEx` | 获取游戏币 | `Player: _TPlayObject` | `Integer` |
| `TPlayObject_SetGameGoldEx` | 设置游戏币 | `Player: _TPlayObject; Value: Integer` | 无 |
| `TPlayObject_GetGameGlory` | 获取荣誉 | `Player: _TPlayObject` | `Integer` |
| `TPlayObject_SetGameGlory` | 设置荣誉 | `Player: _TPlayObject; Value: Integer` | 无 |
| `TPlayObject_IncGameGlory` | 增加荣誉 | `Player: _TPlayObject; Value: Integer` | 无 |
| `TPlayObject_DecGameGlory` | 减少荣誉 | `Player: _TPlayObject; Value: Integer` | 无 |
| `TPlayObject_GameGloryChanged` | 通知客户端刷新荣誉 | `Player: _TPlayObject` | 无 |
| `TPlayObject_GetPayMentPoint` | 获取充值点 | `Player: _TPlayObject` | `Integer` |
| `TPlayObject_SetPayMentPoint` | 设置充值点 | `Player: _TPlayObject; Value: Integer` | 无 |

**会员系统：**

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TPlayObject_GetMemberType` | 获取会员类型 | `Player: _TPlayObject` | `Integer` |
| `TPlayObject_SetMemberType` | 设置会员类型 | `Player: _TPlayObject; Value: Integer` | 无 |
| `TPlayObject_GetMemberLevel` | 获取会员等级 | `Player: _TPlayObject` | `Integer` |
| `TPlayObject_SetMemberLevel` | 设置会员等级 | `Player: _TPlayObject; Value: Integer` | 无 |
| `TPlayObject_GetContribution` | 获取贡献度 | `Player: _TPlayObject` | `Word` |
| `TPlayObject_SetContribution` | 设置贡献度 | `Player: _TPlayObject; Value: Word` | 无 |

---

## 第二部分：客户端API接口

### 1. 客户端核心API结构

客户端API通过 `TClientAPI` 记录结构提供，包含以下主要组件：

```pascal
TClientAPI = record
  ListAPI: TListAPI;                    // 列表管理API
  StringListAPI: TStringListAPI;        // 字符串列表API
  ItemMenuListAPI: TItemMenuListAPI;    // 菜单项API
  TextureAPI: TTextureAPI;              // 纹理API
  ImagesAPI: TImagesAPI;                // 图像资源API
  InterfaceAPI: TInterfaceAPI;          // 游戏界面API
  DrawAPI: TDrawAPI;                    // 绘图API
  ActorAPI: TActorAPI;                  // 角色对象API
  SocketAPI: TSocketAPI;                // 网络通信API
  HookAPI: THookAPI;                    // Hook系统API
  GameAPI: TGameAPI;                    // 游戏逻辑API
  GameInterfaceAPI: TGameInterfaceAPI;  // 游戏界面扩展API
end;
```

### 2. 列表管理API (TListAPI)

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `Create` | 创建列表 | 无 | `TList` |
| `Free` | 释放列表 | `List: TList` | 无 |
| `Clear` | 清空列表 | `List: TList` | 无 |
| `Count` | 获取元素数量 | `List: TList` | `Integer` |
| `Add` | 添加元素 | `List: TList; Item: Pointer` | `Integer` |
| `Insert` | 插入元素 | `List: TList; Index: Integer; Item: Pointer` | 无 |
| `Delete` | 删除指定索引元素 | `List: TList; Index: Integer` | 无 |
| `Remove` | 删除指定元素 | `List: TList; Item: Pointer` | `Integer` |
| `IndexOf` | 查找元素索引 | `List: TList; Item: Pointer` | `Integer` |
| `GetItem` | 获取指定索引元素 | `List: TList; Index: Integer` | `Pointer` |
| `SetItem` | 设置指定索引元素 | `List: TList; Index: Integer; Item: Pointer` | 无 |

### 3. 字符串列表API (TStringListAPI)

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `Create` | 创建字符串列表 | 无 | `TStringList` |
| `Free` | 释放字符串列表 | `Strings: TStringList` | 无 |
| `Clear` | 清空字符串列表 | `Strings: TStringList` | 无 |
| `Count` | 获取字符串数量 | `Strings: TStringList` | `Integer` |
| `Add` | 添加字符串 | `Strings: TStringList; S: PChar` | `Integer` |
| `Insert` | 插入字符串 | `Strings: TStringList; Index: Integer; S: PChar` | 无 |
| `Delete` | 删除指定索引字符串 | `Strings: TStringList; Index: Integer` | 无 |
| `IndexOf` | 查找字符串索引 | `Strings: TStringList; S: PChar` | `Integer` |
| `GetText` | 获取所有文本 | `Strings: TStringList` | `PChar` |
| `SetText` | 设置所有文本 | `Strings: TStringList; Text: PChar` | 无 |
| `GetString` | 获取指定索引字符串 | `Strings: TStringList; Index: Integer` | `PChar` |
| `SetString` | 设置指定索引字符串 | `Strings: TStringList; Index: Integer; S: PChar` | 无 |

### 4. 图像资源API (TImagesAPI)

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `GetCachedImage` | 获取缓存图像 | `ImageIndex: Integer; var px, py: Integer` | `TTexture` |
| `GetWilImageInfo` | 获取WIL图像信息 | `FileName: PChar; ImageIndex: Integer; var Width, Height: Integer` | `Boolean` |
| `LoadWilImageTexture` | 加载WIL图像纹理 | `FileName: PChar; ImageIndex: Integer` | `TTexture` |
| `FreeTexture` | 释放纹理 | `Texture: TTexture` | 无 |

### 5. 绘图API (TDrawAPI)

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TextOut` | 输出文本 | `X, Y: Integer; Text: PChar; Color: TColor` | 无 |
| `TextOutEx` | 输出文本(扩展) | `X, Y: Integer; Text: PChar; Color: TColor; BackColor: TColor` | 无 |
| `TextWidth` | 计算文本宽度 | `Text: PChar` | `Integer` |
| `TextHeight` | 计算文本高度 | `Text: PChar` | `Integer` |
| `DrawTexture` | 绘制纹理 | `Texture: TTexture; X, Y: Integer` | 无 |
| `DrawTextureEx` | 绘制纹理(扩展) | `Texture: TTexture; X, Y: Integer; Blend: Boolean` | 无 |
| `DrawLine` | 绘制直线 | `X1, Y1, X2, Y2: Integer; Color: TColor` | 无 |
| `DrawRect` | 绘制矩形 | `X, Y, Width, Height: Integer; Color: TColor` | 无 |
| `FillRect` | 填充矩形 | `X, Y, Width, Height: Integer; Color: TColor` | 无 |

### 6. 角色对象API (TActorAPI)

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `m_wAppearance` | 获取外观ID指针 | `Actor: TActor` | `PWord` |
| `m_nRecogId` | 获取角色识别ID指针 | `Actor: TActor` | `PInt64` |
| `m_nCurrX` | 获取当前X坐标指针 | `Actor: TActor` | `PInteger` |
| `m_nCurrY` | 获取当前Y坐标指针 | `Actor: TActor` | `PInteger` |
| `m_btDir` | 获取当前站立方向指针 | `Actor: TActor` | `PByte` |
| `m_btSex` | 获取性别指针 | `Actor: TActor` | `PByte` |
| `m_btRace` | 获取种族指针 | `Actor: TActor` | `PByte` |
| `m_btHair` | 获取头发样式指针 | `Actor: TActor` | `PByte` |
| `m_wDress` | 获取衣服样式指针 | `Actor: TActor` | `PWord` |
| `m_wWeapon` | 获取武器样式指针 | `Actor: TActor` | `PWord` |
| `m_btJob` | 获取职业指针 | `Actor: TActor` | `PByte` |
| `m_sDescUserName` | 获取显示名称指针 | `Actor: TActor` | `PChar` |
| `m_sUserName` | 获取角色名指针 | `Actor: TActor` | `PChar` |
| `m_nNameColor` | 获取名字颜色指针 | `Actor: TActor` | `PInteger` |
| `m_Abil` | 获取能力值指针 | `Actor: TActor` | `pTAbility` |
| `m_boOpenShop` | 是否在摆摊 | `Actor: TActor` | `Boolean` |

### 7. 网络通信API (TSocketAPI)

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `SendSocket` | 发送数据包 | `DefMsg: pTDefaultMessage; Buf: PChar` | 无 |
| `SendSay` | 发送聊天消息 | `Text: PChar` | 无 |
| `SendActMsg` | 发送动作消息 | `Ident: Word; X, Y: Integer` | 无 |
| `SendSpellMsg` | 发送魔法消息 | `Ident: Word; X, Y: Integer; TargetID: Integer; MagicID: Word` | 无 |
| `SendClientMessage` | 发送客户端消息 | `Msg: pTDefaultMessage; nParam1, nParam2, nParam3: Integer; sMsg: PChar` | 无 |

### 8. Hook系统API (THookAPI)

Hook系统允许插件拦截和处理游戏中的各种事件。

**获取Hook函数指针：**

| 函数名 | 功能描述 | 返回值 |
|--------|----------|--------|
| `GetHookInitialize` | 获取初始化Hook | `TInitialize` |
| `GetHookFinalize` | 获取结束Hook | `TStartPro` |
| `GetHookFormKeyDown` | 获取按键按下Hook | `TFormKeyDown` |
| `GetHookFormKeyPress` | 获取按键输入Hook | `TFormKeyPress` |
| `GetHookFormMouseDown` | 获取鼠标按下Hook | `TFormMouseDown` |
| `GetHookFormMouseMove` | 获取鼠标移动Hook | `TFormMouseMove` |
| `GetHookDecodeMessagePacketStart` | 获取消息解码开始Hook | `TDecodeMessagePacket` |
| `GetHookDecodeMessagePacketStop` | 获取消息解码结束Hook | `TDecodeMessagePacket` |
| `GetHookDecodeMessagePacket` | 获取消息解码Hook | `TDecodeMessagePacket` |
| `GetHookDrawScene1` | 获取场景绘制Hook1 | `TStartPro` |
| `GetHookDrawScene2` | 获取场景绘制Hook2 | `TStartPro` |
| `GetHookDrawScene3` | 获取场景绘制Hook3 | `TStartPro` |
| `GetHookDrawScene4` | 获取场景绘制Hook4 | `TStartPro` |

**设置Hook函数：**

| 函数名 | 功能描述 | 参数 |
|--------|----------|------|
| `SetHookInitialize` | 设置初始化Hook | `Value: TInitialize` |
| `SetHookFinalize` | 设置结束Hook | `Value: TStartPro` |
| `SetHookFormKeyDown` | 设置按键按下Hook | `Value: TFormKeyDown` |
| `SetHookFormKeyPress` | 设置按键输入Hook | `Value: TFormKeyPress` |
| `SetHookFormMouseDown` | 设置鼠标按下Hook | `Value: TFormMouseDown` |
| `SetHookFormMouseMove` | 设置鼠标移动Hook | `Value: TFormMouseMove` |
| `SetHookDecodeMessagePacketStart` | 设置消息解码开始Hook | `Value: TDecodeMessagePacket` |
| `SetHookDecodeMessagePacketStop` | 设置消息解码结束Hook | `Value: TDecodeMessagePacket` |
| `SetHookDecodeMessagePacket` | 设置消息解码Hook | `Value: TDecodeMessagePacket` |

**Hook函数类型定义：**

```pascal
TInitialize = procedure; stdcall;
TStartPro = procedure; stdcall;
TFormKeyDown = procedure(Sender: TObject; var Key: Word; Shift: TShiftState); stdcall;
TFormKeyPress = procedure(Sender: TObject; var Key: Char); stdcall;
TFormMouseDown = procedure(Sender: TObject; Button: TMouseButton; Shift: TShiftState; X, Y: Integer); stdcall;
TFormMouseMove = procedure(Sender: TObject; Shift: TShiftState; X, Y: Integer); stdcall;
TDecodeMessagePacket = procedure(DefMsg: pTDefaultMessage; sData: PChar); stdcall;
```

### 9. 游戏逻辑API (TGameAPI)

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `GetMyself` | 获取当前玩家对象 | 无 | `TActor` |
| `GetMouseCurrX` | 获取鼠标当前X坐标 | 无 | `Integer` |
| `GetMouseCurrY` | 获取鼠标当前Y坐标 | 无 | `Integer` |
| `GetMouseMapX` | 获取鼠标地图X坐标 | 无 | `Integer` |
| `GetMouseMapY` | 获取鼠标地图Y坐标 | 无 | `Integer` |
| `GetTargetCret` | 获取当前目标 | 无 | `TActor` |
| `GetMagicTarget` | 获取魔法目标 | 无 | `TActor` |
| `GetFocusCret` | 获取焦点对象 | 无 | `TActor` |
| `GetMySelf` | 获取自己的角色对象 | 无 | `TActor` |
| `EncodeBuffer` | 编码缓冲区 | `Src: PChar; SrcLen: Integer; Dest: PChar` | `Integer` |
| `DecodeBuffer` | 解码缓冲区 | `Src: PChar; SrcLen: Integer; Dest: PChar` | `Integer` |

### 10. 游戏界面API (TGameInterfaceAPI)

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `GetDScreen` | 获取主屏幕对象 | 无 | `TDScreen` |
| `GetDXDraw` | 获取DirectX绘图对象 | 无 | `TDXDraw` |
| `GetMainForm` | 获取主窗体 | 无 | `TForm` |
| `GetLoginScene` | 获取登录场景 | 无 | `TScene` |
| `GetSelectChrScene` | 获取选择角色场景 | 无 | `TScene` |
| `GetPlayScene` | 获取游戏场景 | 无 | `TScene` |

### 11. 控件API

#### 11.1 基础控件API (TDControl)

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `GetLeft` | 获取左边距 | `Control: TDControl` | `Integer` |
| `SetLeft` | 设置左边距 | `Control: TDControl; Value: Integer` | 无 |
| `GetTop` | 获取上边距 | `Control: TDControl` | `Integer` |
| `SetTop` | 设置上边距 | `Control: TDControl; Value: Integer` | 无 |
| `GetWidth` | 获取宽度 | `Control: TDControl` | `Integer` |
| `SetWidth` | 设置宽度 | `Control: TDControl; Value: Integer` | 无 |
| `GetHeight` | 获取高度 | `Control: TDControl` | `Integer` |
| `SetHeight` | 设置高度 | `Control: TDControl; Value: Integer` | 无 |
| `GetVisible` | 获取可见性 | `Control: TDControl` | `Boolean` |
| `SetVisible` | 设置可见性 | `Control: TDControl; Value: Boolean` | 无 |
| `GetEnabled` | 获取启用状态 | `Control: TDControl` | `Boolean` |
| `SetEnabled` | 设置启用状态 | `Control: TDControl; Value: Boolean` | 无 |

#### 11.2 窗口控件API (TDWindow)

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `Create` | 创建窗口 | `AOwner: TDControl` | `TDWindow` |
| `Free` | 释放窗口 | `Window: TDWindow` | 无 |
| `GetCaption` | 获取标题 | `Window: TDWindow` | `PChar` |
| `SetCaption` | 设置标题 | `Window: TDWindow; Value: PChar` | 无 |
| `GetSpaceX` | 获取X间距 | `Window: TDWindow` | `Integer` |
| `SetSpaceX` | 设置X间距 | `Window: TDWindow; Value: Integer` | 无 |
| `GetSpaceY` | 获取Y间距 | `Window: TDWindow` | `Integer` |
| `SetSpaceY` | 设置Y间距 | `Window: TDWindow; Value: Integer` | 无 |

#### 11.3 按钮控件API (TDButton)

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `Create` | 创建按钮 | `AOwner: TDControl` | `TDButton` |
| `Free` | 释放按钮 | `Button: TDButton` | 无 |
| `GetCaption` | 获取按钮文本 | `Button: TDButton` | `PChar` |
| `SetCaption` | 设置按钮文本 | `Button: TDButton; Value: PChar` | 无 |
| `GetOnClick` | 获取点击事件 | `Button: TDButton` | `TNotifyEvent` |
| `SetOnClick` | 设置点击事件 | `Button: TDButton; Value: TNotifyEvent` | 无 |

---

## 第三部分：NPC脚本命令系统

### 1. NPC脚本命令系统概述

NPC脚本命令系统允许开发者创建自定义的NPC脚本命令，扩展游戏的脚本功能。系统分为两个部分：
- **条件命令 (Condition Commands)**: 用于检查游戏状态的条件
- **动作命令 (Action Commands)**: 用于执行具体的游戏操作

### 2. 插件导出函数

每个NPC脚本命令插件必须导出以下函数：

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `Init` | 插件初始化 | `AppFunc: pTAppFunc` | `BOOL` |
| `UnInit` | 插件卸载 | 无 | `BOOL` |
| `HookNpcLoadConditionCmd` | 注册条件命令 | `CmdName: PAnsiChar; CmdProc: Pointer` | `BOOL` |
| `HookNpcConditionProcess` | 处理条件命令 | `PlayObject: _TPlayObject; CmdName, CmdParam: PAnsiChar` | `BOOL` |
| `HookNpcLoadActionCmd` | 注册动作命令 | `CmdName: PAnsiChar; CmdProc: Pointer` | `BOOL` |
| `HookNpcActionProcess` | 处理动作命令 | `PlayObject: _TPlayObject; CmdName, CmdParam: PAnsiChar` | `BOOL` |

### 3. 条件命令示例

**示例1：等级检查命令**

```pascal
// 条件检查函数
function MyCheckLevel(PlayObject: _TPlayObject; sParam: PAnsiChar): BOOL; stdcall;
var
  nLevel: Integer;
begin
  Result := False;
  try
    nLevel := StrToIntDef(string(sParam), 0);
    if g_AppFunc.BaseObject.GetLevel(PlayObject) >= nLevel then
      Result := True;
  except
    Result := False;
  end;
end;

// 注册条件命令
function HookNpcLoadConditionCmd(CmdName: PAnsiChar; CmdProc: Pointer): BOOL; stdcall;
begin
  Result := False;
  if StrComp(CmdName, 'CHECKLEVEL') = 0 then begin
    Pointer(CmdProc^) := @MyCheckLevel;
    Result := True;
  end;
end;
```

**使用方法：**
```
#IF
CHECKLEVEL 35
#ACT
give 金币 1000
#ELSESAY
你的等级不够35级！
```

**示例2：职业检查命令**

```pascal
// 职业检查函数
function MyCheckJob(PlayObject: _TPlayObject; sParam: PAnsiChar): BOOL; stdcall;
var
  nJob: Integer;
begin
  Result := False;
  try
    nJob := StrToIntDef(string(sParam), -1);
    if g_AppFunc.BaseObject.GetJob(PlayObject) = nJob then
      Result := True;
  except
    Result := False;
  end;
end;

// 注册条件命令
function HookNpcLoadConditionCmd(CmdName: PAnsiChar; CmdProc: Pointer): BOOL; stdcall;
begin
  Result := False;
  if StrComp(CmdName, 'CHECKJOB') = 0 then begin
    Pointer(CmdProc^) := @MyCheckJob;
    Result := True;
  end;
end;
```

**使用方法：**
```
#IF
CHECKJOB 0
#ACT
give 屠龙 1
#ELSESAY
只有战士才能获得屠龙！
```

### 4. 动作命令示例

**示例1：发送消息命令**

```pascal
// 发送消息函数
function MySendMsg(PlayObject: _TPlayObject; sParam: PAnsiChar): BOOL; stdcall;
var
  sMsg: string;
begin
  Result := True;
  try
    sMsg := string(sParam);
    g_AppFunc.BaseObject.SysMsg(PlayObject, PAnsiChar(sMsg),
                               c_Green, c_Red, t_Notice);
  except
    Result := False;
  end;
end;

// 注册动作命令
function HookNpcLoadActionCmd(CmdName: PAnsiChar; CmdProc: Pointer): BOOL; stdcall;
begin
  Result := False;
  if StrComp(CmdName, 'SENDMSG') = 0 then begin
    Pointer(CmdProc^) := @MySendMsg;
    Result := True;
  end;
end;
```

**使用方法：**
```
#ACT
SENDMSG 欢迎来到我的世界！
```

**示例2：自定义传送命令**

```pascal
// 传送函数
function MyMapMove(PlayObject: _TPlayObject; sParam: PAnsiChar): BOOL; stdcall;
var
  sParams: TStringList;
  sMapName: string;
  nX, nY: Integer;
begin
  Result := True;
  try
    sParams := TStringList.Create;
    try
      sParams.CommaText := string(sParam);
      if sParams.Count >= 3 then begin
        sMapName := sParams[0];
        nX := StrToIntDef(sParams[1], 0);
        nY := StrToIntDef(sParams[2], 0);
        g_AppFunc.BaseObject.SpaceMove(PlayObject, PAnsiChar(sMapName), nX, nY, 0);
      end;
    finally
      sParams.Free;
    end;
  except
    Result := False;
  end;
end;

// 注册动作命令
function HookNpcLoadActionCmd(CmdName: PAnsiChar; CmdProc: Pointer): BOOL; stdcall;
begin
  Result := False;
  if StrComp(CmdName, 'MAPMOVE') = 0 then begin
    Pointer(CmdProc^) := @MyMapMove;
    Result := True;
  end;
end;
```

**使用方法：**
```
#ACT
MAPMOVE 3,330,330
```

### 5. 脚本命令处理流程

1. **插件加载**: M2引擎加载插件DLL，调用 `Init` 函数
2. **命令注册**: 引擎调用 `HookNpcLoadConditionCmd` 和 `HookNpcLoadActionCmd` 注册命令
3. **脚本解析**: 当NPC脚本被解析时，引擎识别自定义命令
4. **命令执行**:
   - 条件命令：调用 `HookNpcConditionProcess`
   - 动作命令：调用 `HookNpcActionProcess`
5. **插件卸载**: 引擎关闭时调用 `UnInit` 函数

### 6. 注意事项

- **线程安全**: 所有函数都在主线程中调用，无需考虑线程同步
- **异常处理**: 必须在函数中处理所有可能的异常，避免崩溃
- **内存管理**: 注意字符串和对象的内存管理，避免内存泄漏
- **参数验证**: 对所有输入参数进行有效性检查
- **返回值**: 条件命令返回True表示条件满足，动作命令返回True表示执行成功

---

## 第四部分：自定义菜单系统

### 1. 自定义菜单系统概述

自定义菜单系统允许开发者为游戏添加自定义的右键菜单功能，可以创建层级菜单结构，为玩家提供更丰富的交互体验。

### 2. 菜单插件导出函数

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `Init` | 插件初始化 | `AppFunc: pTAppFunc` | `BOOL` |
| `UnInit` | 插件卸载 | 无 | `BOOL` |
| `GetMenuCount` | 获取菜单数量 | 无 | `Integer` |
| `GetMenuInfo` | 获取菜单信息 | `Index: Integer; MenuInfo: pTMenuInfo` | `BOOL` |
| `MenuClick` | 菜单点击事件 | `PlayObject: _TPlayObject; MenuIndex: Integer; MenuID: Integer` | `BOOL` |

### 3. 菜单信息结构

```pascal
TMenuInfo = record
  MenuID: Integer;        // 菜单ID
  ParentID: Integer;      // 父菜单ID (0表示根菜单)
  Caption: array[0..63] of AnsiChar;  // 菜单标题
  Enabled: Boolean;       // 是否启用
  Visible: Boolean;       // 是否可见
  Checked: Boolean;       // 是否选中
end;
```

