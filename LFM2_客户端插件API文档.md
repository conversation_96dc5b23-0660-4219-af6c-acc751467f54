# LFM2 客户端插件API文档

## 项目概述

LFM2客户端插件系统基于Delphi开发，通过DLL插件的形式扩展游戏客户端功能。插件系统提供了丰富的API接口，支持UI界面扩展、游戏画面绘制、网络通信、Hook系统等功能。

## 目录结构

```
客户端插件/Delphi/
├── Common/                      # API接口定义
│   ├── ClientAPI.pas           # 客户端API接口
│   └── ClientType.pas          # 客户端类型定义
├── Demo/                       # 基础插件示例
│   └── PlugMain.pas
├── cPlugOfUserShop/            # 用户商店插件示例
│   └── PlugMain.pas
└── cPlugOfMiniMap/             # 小地图插件示例
    └── PlugMain.pas
```

---

## 第一部分：客户端核心API结构

客户端API通过 `TClientAPI` 记录结构提供，包含以下主要组件：

```pascal
TClientAPI = record
  ListAPI: TListAPI;                    // 列表管理API
  StringListAPI: TStringListAPI;        // 字符串列表API
  ItemMenuListAPI: TItemMenuListAPI;    // 菜单项API
  TextureAPI: TTextureAPI;              // 纹理API
  ImagesAPI: TImagesAPI;                // 图像资源API
  InterfaceAPI: TInterfaceAPI;          // 游戏界面API
  DrawAPI: TDrawAPI;                    // 绘图API
  ActorAPI: TActorAPI;                  // 角色对象API
  SocketAPI: TSocketAPI;                // 网络通信API
  HookAPI: THookAPI;                    // Hook系统API
  GameAPI: TGameAPI;                    // 游戏逻辑API
  GameInterfaceAPI: TGameInterfaceAPI;  // 游戏界面扩展API
  PointDropItemList: TPointDropItemListAPI;  // 掉落物品列表API
  DropItemsMgr: TDropItemsMgrAPI;       // 掉落物品管理API
end;
```

---

## 第二部分：基础数据结构API

### 2.1 列表管理API (TListAPI)

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `Create` | 创建列表 | 无 | `TList` |
| `Free` | 释放列表 | `List: TList` | 无 |
| `Clear` | 清空列表 | `List: TList` | 无 |
| `Count` | 获取元素数量 | `List: TList` | `Integer` |
| `Add` | 添加元素 | `List: TList; Item: Pointer` | `Integer` |
| `Insert` | 插入元素 | `List: TList; Index: Integer; Item: Pointer` | 无 |
| `Delete` | 删除指定索引元素 | `List: TList; Index: Integer` | 无 |
| `Remove` | 删除指定元素 | `List: TList; Item: Pointer` | `Integer` |
| `IndexOf` | 查找元素索引 | `List: TList; Item: Pointer` | `Integer` |
| `GetItem` | 获取指定索引元素 | `List: TList; Index: Integer` | `Pointer` |
| `SetItem` | 设置指定索引元素 | `List: TList; Index: Integer; Item: Pointer` | 无 |

### 2.2 字符串列表API (TStringListAPI)

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `Create` | 创建字符串列表 | 无 | `TStringList` |
| `Free` | 释放字符串列表 | `Strings: TStringList` | 无 |
| `Clear` | 清空字符串列表 | `Strings: TStringList` | 无 |
| `Count` | 获取字符串数量 | `Strings: TStringList` | `Integer` |
| `Add` | 添加字符串 | `Strings: TStringList; S: PChar` | `Integer` |
| `Insert` | 插入字符串 | `Strings: TStringList; Index: Integer; S: PChar` | 无 |
| `Delete` | 删除指定索引字符串 | `Strings: TStringList; Index: Integer` | 无 |
| `IndexOf` | 查找字符串索引 | `Strings: TStringList; S: PChar` | `Integer` |
| `GetText` | 获取所有文本 | `Strings: TStringList` | `PChar` |
| `SetText` | 设置所有文本 | `Strings: TStringList; Text: PChar` | 无 |
| `GetString` | 获取指定索引字符串 | `Strings: TStringList; Index: Integer` | `PChar` |
| `SetString` | 设置指定索引字符串 | `Strings: TStringList; Index: Integer; S: PChar` | 无 |

---

## 第三部分：图像和绘图API

### 3.1 图像资源API (TImagesAPI)

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `GetCachedImage` | 获取缓存图像 | `ImageIndex: Integer; var px, py: Integer` | `TTexture` |
| `GetWilImageInfo` | 获取WIL图像信息 | `FileName: PChar; ImageIndex: Integer; var Width, Height: Integer` | `Boolean` |
| `LoadWilImageTexture` | 加载WIL图像纹理 | `FileName: PChar; ImageIndex: Integer` | `TTexture` |
| `FreeTexture` | 释放纹理 | `Texture: TTexture` | 无 |

**使用示例：**
```pascal
var
  Texture: TTexture;
  Width, Height: Integer;
begin
  // 获取WIL图像信息
  if ImagesAPI.GetWilImageInfo('Prguse.wil', 100, Width, Height) then
  begin
    // 加载纹理
    Texture := ImagesAPI.LoadWilImageTexture('Prguse.wil', 100);
    if Texture <> nil then
    begin
      // 使用纹理进行绘制
      DrawAPI.DrawTexture(Texture, 100, 100);
      // 释放纹理
      ImagesAPI.FreeTexture(Texture);
    end;
  end;
end;
```

### 3.2 绘图API (TDrawAPI)

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `TextOut` | 输出文本 | `X, Y: Integer; Text: PChar; Color: TColor` | 无 |
| `TextOutEx` | 输出文本(扩展) | `X, Y: Integer; Text: PChar; Color: TColor; BackColor: TColor` | 无 |
| `TextWidth` | 计算文本宽度 | `Text: PChar` | `Integer` |
| `TextHeight` | 计算文本高度 | `Text: PChar` | `Integer` |
| `DrawTexture` | 绘制纹理 | `Texture: TTexture; X, Y: Integer` | 无 |
| `DrawTextureEx` | 绘制纹理(扩展) | `Texture: TTexture; X, Y: Integer; Blend: Boolean` | 无 |
| `DrawLine` | 绘制直线 | `X1, Y1, X2, Y2: Integer; Color: TColor` | 无 |
| `DrawRect` | 绘制矩形 | `X, Y, Width, Height: Integer; Color: TColor` | 无 |
| `FillRect` | 填充矩形 | `X, Y, Width, Height: Integer; Color: TColor` | 无 |

**使用示例：**
```pascal
procedure DrawCustomUI;
begin
  // 绘制背景矩形
  DrawAPI.FillRect(100, 100, 200, 150, $FF000080);
  
  // 绘制边框
  DrawAPI.DrawRect(100, 100, 200, 150, $FFFFFFFF);
  
  // 绘制标题文本
  DrawAPI.TextOut(110, 110, '自定义窗口', $FFFFFF00);
  
  // 绘制内容文本
  DrawAPI.TextOut(110, 130, '这是一个自定义界面', $FFFFFFFF);
end;
```

---

## 第四部分：角色对象API

### 4.1 角色对象API (TActorAPI)

| 函数名 | 功能描述 | 参数 | 返回值 |
|--------|----------|------|--------|
| `m_wAppearance` | 获取外观ID指针 | `Actor: TActor` | `PWord` |
| `m_nRecogId` | 获取角色识别ID指针 | `Actor: TActor` | `PInt64` |
| `m_nCurrX` | 获取当前X坐标指针 | `Actor: TActor` | `PInteger` |
| `m_nCurrY` | 获取当前Y坐标指针 | `Actor: TActor` | `PInteger` |
| `m_btDir` | 获取当前站立方向指针 | `Actor: TActor` | `PByte` |
| `m_btSex` | 获取性别指针 | `Actor: TActor` | `PByte` |
| `m_btRace` | 获取种族指针 | `Actor: TActor` | `PByte` |
| `m_btHair` | 获取头发样式指针 | `Actor: TActor` | `PByte` |
| `m_wDress` | 获取衣服样式指针 | `Actor: TActor` | `PWord` |
| `m_wWeapon` | 获取武器样式指针 | `Actor: TActor` | `PWord` |
| `m_btJob` | 获取职业指针 | `Actor: TActor` | `PByte` |
| `m_btCaseltGuild` | 获取行会标识指针 | `Actor: TActor` | `PByte` |
| `m_sDescUserName` | 获取显示名称指针 | `Actor: TActor` | `PChar` |
| `m_sUserName` | 获取角色名指针 | `Actor: TActor` | `PChar` |
| `m_nNameColor` | 获取名字颜色指针 | `Actor: TActor` | `PInteger` |
| `m_Abil` | 获取能力值指针 | `Actor: TActor` | `pTAbility` |
| `m_boOpenShop` | 是否在摆摊 | `Actor: TActor` | `Boolean` |

**使用示例：**
```pascal
procedure ShowActorInfo(Actor: TActor);
var
  Name: PChar;
  Level: Integer;
  Job: Byte;
begin
  if Actor <> nil then
  begin
    Name := ActorAPI.m_sUserName(Actor);
    Job := ActorAPI.m_btJob(Actor)^;
    Level := ActorAPI.m_Abil(Actor)^.Level;
    
    DrawAPI.TextOut(10, 10, PChar(Format('角色: %s', [string(Name)])), $FFFFFF00);
    DrawAPI.TextOut(10, 30, PChar(Format('职业: %d', [Job])), $FFFFFF00);
    DrawAPI.TextOut(10, 50, PChar(Format('等级: %d', [Level])), $FFFFFF00);
  end;
end;
```
